#include "ALLHeader.h"

int32_t Set_Speed_L,Set_Speed_R,Set_Speed,Read_Speed_R,Read_Speed_L;
int32_t OutL,OutR;
float PL,IL,PR,IR,S;
int ek1,ek2,ek3,ek4;

void Set_PWM(int PWMA,int PWMB)
{

//	  if(PWMA>=0&&PWMB>=0)
//		{
//			DL_GPIO_setPins(AIN1_PORT,AIN1_PIN_1_PIN);	
////			DL_GPIO_clearPins(AIN2_PORT,AIN2_PIN_2_PIN);	
//			DL_GPIO_setPins(BIN1_PORT,BIN1_PIN_3_PIN);	
////			DL_GPIO_clearPins(BIN2_PORT,BIN2_PIN_4_PIN);	
//			
//			DL_Timer_setCaptureCompareValue(PWM_A_B_INST,PWMA,GPIO_PWM_A_B_C0_IDX);///L+
//			DL_Timer_setCaptureCompareValue(PWM_A_B_INST,PWMB,GPIO_PWM_A_B_C1_IDX);//R+		
//		}
//		else if(PWMA>=0&&PWMB<0)
//		{
//			DL_GPIO_setPins(AIN1_PORT,AIN1_PIN_1_PIN);	
////			DL_GPIO_clearPins(AIN2_PORT,AIN2_PIN_2_PIN);	
//			DL_GPIO_clearPins(BIN1_PORT,BIN1_PIN_3_PIN);	
////			DL_GPIO_setPins(BIN2_PORT,BIN2_PIN_4_PIN);
//			
//			DL_Timer_setCaptureCompareValue(PWM_A_B_INST,PWMA,GPIO_PWM_A_B_C0_IDX);///L+
//			DL_Timer_setCaptureCompareValue(PWM_A_B_INST,PWMB,GPIO_PWM_A_B_C1_IDX);//R+		
//		}
//		else if(PWMA<0&&PWMB>=0)
//		{
//			DL_GPIO_clearPins(AIN1_PORT,AIN1_PIN_1_PIN);	
////			DL_GPIO_setPins(AIN2_PORT,AIN2_PIN_2_PIN);	
//			DL_GPIO_setPins(BIN1_PORT,BIN1_PIN_3_PIN);	
////			DL_GPIO_clearPins(BIN2_PORT,BIN2_PIN_4_PIN);
//			
//			DL_Timer_setCaptureCompareValue(PWM_A_B_INST,PWMA,GPIO_PWM_A_B_C0_IDX);///L+
//			DL_Timer_setCaptureCompareValue(PWM_A_B_INST,PWMB,GPIO_PWM_A_B_C1_IDX);//R+		
//		}		
//		else if(PWMA<0&&PWMB<0)
//		{
//			DL_GPIO_clearPins(AIN1_PORT,AIN1_PIN_1_PIN);	
////			DL_GPIO_setPins(AIN2_PORT,AIN2_PIN_2_PIN);	
//			DL_GPIO_clearPins(BIN1_PORT,BIN1_PIN_3_PIN);	
////			DL_GPIO_setPins(BIN2_PORT,BIN2_PIN_4_PIN);
//			
//			DL_Timer_setCaptureCompareValue(PWM_A_B_INST,PWMA,GPIO_PWM_A_B_C0_IDX);///L+
//			DL_Timer_setCaptureCompareValue(PWM_A_B_INST,PWMB,GPIO_PWM_A_B_C1_IDX);//R+		
//		}		
}
void Motor_PI(void)
{
	ek1 = Set_Speed_L - Read_Speed_L;   
	OutL+= (float)(PL*(ek1-ek2) + IL*ek1); 
	ek2 = ek1;				
	OutL=OutL>3199? 3199:OutL;
	OutL=OutL<-3199? -3199:OutL;
	
	ek3 = Set_Speed_R - Read_Speed_R;   
	OutR += (float)(PR*(ek3-ek4) + IR*ek3); 
	ek4 = ek3;				
	OutR=OutR>3199? 3199:OutR;
	OutR=OutR<-3199? -3199:OutR;
	Set_PWM(OutL,OutR);
	
	
}
