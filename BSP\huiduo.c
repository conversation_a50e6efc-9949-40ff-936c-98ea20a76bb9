#include "huiduo.h"
#include "ti_msp_dl_config.h"
uint8_t i;
uint16_t HDadc_value[8]={0};
uint16_t HD2adc_value[8]={0};
int16_t er0=0,er1=0;

int16_t sover_ksp=0;
int16_t sover_ksd=0;

int posetion=0,duty=0;

volatile unsigned int delay_times = 0;
volatile bool lsqgCheckADC;        //ADC�ɼ��ɹ���־λ ADC acquisition success flag

#define Switch_Address_0(i) ((i)?(DL_GPIO_setPins(Gray_Address_PIN_0_PORT,Gray_Address_PIN_0_PIN)) : (DL_GPIO_clearPins(Gray_Address_PIN_0_PORT,Gray_Address_PIN_0_PIN)))// ???0??

#define Switch_Address_1(i) ((i)?(DL_GPIO_setPins(Gray_Address_PIN_1_PORT,Gray_Address_PIN_1_PIN)) : (DL_GPIO_clearPins(Gray_Address_PIN_1_PORT,Gray_Address_PIN_1_PIN)))// ???1??

#define Switch_Address_2(i) ((i)?(DL_GPIO_setPins(Gray_Address_PIN_3_PORT,Gray_Address_PIN_3_PIN)) : (DL_GPIO_clearPins(Gray_Address_PIN_3_PORT,Gray_Address_PIN_3_PIN)))// ???2??


void huidu_read(void)
{
	for(i=0;i<8;i++)
	{

//		  DL_GPIO_clearPins(LED1_PORT,LED1_PIN_2_PIN);//����͵�ƽ Output low level
//		  DL_GPIO_setPins(LED1_PORT,LED1_PIN_2_PIN);  //����ߵ�ƽ Output high level
		    Switch_Address_0((i&0x01));  // ???0,??bit0
        Switch_Address_1((i&0x02));  // ???1,??bit1
        Switch_Address_2((i&0x04));  // ???2,??bit2
		    //��ȡADC���� Get ADC data
					
        HDadc_value[i] = lsqadc_getValue();
		if(HDadc_value[i]<2000)
		{
			HDadc_value[i]=1;
		}
		else
		{
			HDadc_value[i]=0;
		}			
		
	}
		for(i=0;i<8;i++)
	{

		Switch_Address_0(!(i&0x01));  // ???0,??bit0
        Switch_Address_1(!(i&0x02));  // ???1,??bit1
        Switch_Address_2(!(i&0x04));  // ???2,??bit2

		HD2adc_value[i] = lsqadc_getValue();
		    
		if(HD2adc_value[i]<2000)
		{
			HD2adc_value[i]=1;
		}
		else
		{
			HD2adc_value[i]=0;
		}	
	}

}





unsigned int lsqadc_getValue(void)
{
        unsigned int gAdcResult = 0;

        //��������ADC��ʼת�� Software triggers ADC to start conversion
        DL_ADC12_startConversion(ADC1_INST);
        //�����ǰ״̬Ϊ����ת������ȴ�ת������
				// If the current state is converting, wait for the conversion to end
//        while (false == lsqgCheckADC) {
//            __WFE();
//        }
        //��ȡ���� Get data
        gAdcResult = DL_ADC12_getMemResult(ADC1_INST, ADC1_ADCMEM_ADC_Channel0);

        //�����־λ Clear flag
        lsqgCheckADC = false;

        return gAdcResult;
}

void ADC_VOLTAGE_INST_IRQHandler(void)
{
        //��ѯ�����ADC�ж� Query and clear ADC interrupt
        switch (DL_ADC12_getPendingInterrupt(ADC1_INST ))
        {
                        //����Ƿ�������ݲɼ� Check whether data collection is completed
                        case DL_ADC12_IIDX_MEM0_RESULT_LOADED:
                                        lsqgCheckADC = true;//����־λ��1 Set the flag position to 1
                                        break;
                        default:
                                        break;
        }
}






void PD_Motor_huidu(void)
{
//					display_6_8_number_f1(3,1,HD2adc_value[7]);//��ʾ������ֵ
//          display_6_8_number_f1(3,3,HDadc_value[1]);//��ʾ������ֵ
//			    display_6_8_number_f1(3,5,HDadc_value[2]);//��ʾ������ֵ
//			    display_6_8_number_f1(3,7,HDadc_value[3]);//��ʾ������ֵ
//			    display_6_8_number_f1(30,1,HDadc_value[4]);//��ʾ������ֵ
//			    display_6_8_number_f1(30,3,HDadc_value[5]);//��ʾ������ֵ
//			    display_6_8_number_f1(30,5,HDadc_value[6]);//��ʾ������ֵ
//			    display_6_8_number_f1(30,7,HDadc_value[7]);//��ʾ������ֵ
	 if(HD2adc_value[7]<2000){posetion += 16;}
	 if(HDadc_value[1]<2000){posetion += 8;}
	 if(HDadc_value[2]<2000){posetion += 6;}
	 if(HDadc_value[3]<2000){posetion += 2;}
	 if(HDadc_value[4]<2000){posetion -= 2;}
	 if(HDadc_value[5]<2000){posetion -= 6;}
	 if(HDadc_value[6]<2000){posetion -= 8;}
	 if(HDadc_value[7]<2000){posetion -= 16;}	 
	 
	 er0=posetion;
	 duty=er0*sover_ksp+(er0-er1)*sover_ksd;
	 er1=er0;
	 
	
}

