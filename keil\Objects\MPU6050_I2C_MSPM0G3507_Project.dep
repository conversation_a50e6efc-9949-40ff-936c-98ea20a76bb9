Dependencies for Project 'MPU6050_I2C', Target 'MSPM0G3507_Project': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (../empty.c)(0x6889B7D6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/empty.o -MMD)
I (..\board.h)(0x67500006)
I (..\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
I (..\BSP\bsp_mpu6050.h)(0x675001DA)
I (..\..\10_I2C\board.h)(0x67500006)
I (..\BSP\eMPL\inv_mpu.h)(0x66AA1CDE)
I (..\BSP\drv_oled.h)(0x666EA682)
I (..\ALLHeader.h)(0x688992ED)
I (..\BSP\usart.h)(0x68873FBA)
I (..\..\10_I2C\ALLHeader.h)(0x688992ED)
I (..\BSP\ccd.h)(0x68873FB8)
I (..\BSP\huiduo.h)(0x6889AE32)
I (..\BSP\Motor.h)(0x681A111D)
I (..\BSP\Control.h)(0x6889AC1C)
I (..\BSP\ADC.h)(0x688992C6)
F (../empty.syscfg)(0x6889A50D)()
F (startup_mspm0g350x_uvision.s)(0x65B1D8C4)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-Wa,armasm,--pd,"__UVISION_VERSION SETA 542" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (../ti_msp_dl_config.h)(0x6889A533)()
F (../ti_msp_dl_config.c)(0x6889A52B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (..\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
F (..\board.c)(0x674FFFF6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/board.o -MMD)
I (..\board.h)(0x67500006)
I (..\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
F (..\ALLHeader.h)(0x688992ED)()
F (..\BSP\bsp_mpu6050.c)(0x6750070C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/bsp_mpu6050.o -MMD)
I (..\BSP\bsp_mpu6050.h)(0x675001DA)
I (..\..\10_I2C\board.h)(0x67500006)
I (..\..\10_I2C\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
F (..\BSP\eMPL\inv_mpu.c)(0x67500352)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/inv_mpu.o -MMD)
I (..\BSP\eMPL\inv_mpu.h)(0x66AA1CDE)
I (..\..\10_I2C\board.h)(0x67500006)
I (..\..\10_I2C\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
I (..\BSP\eMPL\inv_mpu_dmp_motion_driver.h)(0x66AA1CDE)
I (..\BSP\bsp_mpu6050.h)(0x675001DA)
F (..\BSP\eMPL\inv_mpu_dmp_motion_driver.c)(0x675003B0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/inv_mpu_dmp_motion_driver.o -MMD)
I (..\BSP\eMPL\inv_mpu.h)(0x66AA1CDE)
I (..\..\10_I2C\board.h)(0x67500006)
I (..\..\10_I2C\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
I (..\BSP\eMPL\inv_mpu_dmp_motion_driver.h)(0x66AA1CDE)
I (..\BSP\eMPL\dmpKey.h)(0x66AA1CDE)
I (..\BSP\eMPL\dmpmap.h)(0x66AA1CDE)
F (..\BSP\drv_oled.c)(0x6818FEE8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/drv_oled.o -MMD)
I (..\..\10_I2C\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
I (..\BSP\ssd1306.h)(0x666EA682)
I (..\BSP\drv_oled.h)(0x666EA682)
F (..\BSP\drv_oled.h)(0x666EA682)()
F (..\BSP\ssd1306.c)(0x666EA682)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ssd1306.o -MMD)
I (..\..\10_I2C\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
I (..\BSP\binary.h)(0x666EA682)
I (..\BSP\drv_oled.h)(0x666EA682)
I (..\BSP\ssd1306.h)(0x666EA682)
I (..\BSP\glcdfont.c)(0x666EA682)
F (..\BSP\ssd1306.h)(0x666EA682)()
F (..\BSP\binary.h)(0x666EA682)()
F (..\BSP\glcdfont.c)(0x666EA682)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/glcdfont.o -MMD)
F (..\BSP\ccd.c)(0x68886D0E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ccd.o -MMD)
I (..\BSP\ccd.h)(0x68873FB8)
I (..\..\10_I2C\ALLHeader.h)(0x688992ED)
I (..\..\10_I2C\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
I (..\BSP\usart.h)(0x68873FBA)
I (..\..\10_I2C\board.h)(0x67500006)
I (..\BSP\bsp_mpu6050.h)(0x675001DA)
I (..\BSP\eMPL\inv_mpu.h)(0x66AA1CDE)
I (..\BSP\drv_oled.h)(0x666EA682)
I (..\BSP\huiduo.h)(0x6889AE32)
I (..\BSP\Motor.h)(0x681A111D)
I (..\BSP\Control.h)(0x6889AC1C)
I (..\BSP\ADC.h)(0x688992C6)
F (..\BSP\ccd.h)(0x68873FB8)()
F (..\BSP\huiduo.c)(0x6889B5F1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/huiduo.o -MMD)
I (..\BSP\huiduo.h)(0x6889AE32)
I (..\..\10_I2C\ALLHeader.h)(0x688992ED)
I (..\..\10_I2C\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
I (..\BSP\usart.h)(0x68873FBA)
I (..\BSP\ccd.h)(0x68873FB8)
I (..\..\10_I2C\board.h)(0x67500006)
I (..\BSP\bsp_mpu6050.h)(0x675001DA)
I (..\BSP\eMPL\inv_mpu.h)(0x66AA1CDE)
I (..\BSP\drv_oled.h)(0x666EA682)
I (..\BSP\Motor.h)(0x681A111D)
I (..\BSP\Control.h)(0x6889AC1C)
I (..\BSP\ADC.h)(0x688992C6)
F (..\BSP\huiduo.h)(0x6889AE32)()
F (..\BSP\ADC.c)(0x688992C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/adc.o -MMD)
I (..\..\10_I2C\ALLHeader.h)(0x688992ED)
I (..\..\10_I2C\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
I (..\BSP\usart.h)(0x68873FBA)
I (..\BSP\ccd.h)(0x68873FB8)
I (..\..\10_I2C\board.h)(0x67500006)
I (..\BSP\bsp_mpu6050.h)(0x675001DA)
I (..\BSP\eMPL\inv_mpu.h)(0x66AA1CDE)
I (..\BSP\drv_oled.h)(0x666EA682)
I (..\BSP\huiduo.h)(0x6889AE32)
I (..\BSP\Motor.h)(0x681A111D)
I (..\BSP\Control.h)(0x6889AC1C)
I (..\BSP\ADC.h)(0x688992C6)
F (..\BSP\ADC.h)(0x688992C6)()
F (..\BSP\Control.c)(0x6889B1CE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/control.o -MMD)
I (..\..\10_I2C\ALLHeader.h)(0x688992ED)
I (..\..\10_I2C\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
I (..\BSP\usart.h)(0x68873FBA)
I (..\BSP\ccd.h)(0x68873FB8)
I (..\..\10_I2C\board.h)(0x67500006)
I (..\BSP\bsp_mpu6050.h)(0x675001DA)
I (..\BSP\eMPL\inv_mpu.h)(0x66AA1CDE)
I (..\BSP\drv_oled.h)(0x666EA682)
I (..\BSP\huiduo.h)(0x6889AE32)
I (..\BSP\Motor.h)(0x681A111D)
I (..\BSP\Control.h)(0x6889AC1C)
I (..\BSP\ADC.h)(0x688992C6)
F (..\BSP\Control.h)(0x6889AC1C)()
F (..\BSP\Motor.c)(0x688992C6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/motor.o -MMD)
I (..\..\10_I2C\ALLHeader.h)(0x688992ED)
I (..\..\10_I2C\ti_msp_dl_config.h)(0x6889A533)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\driverlib.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_comp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpamp.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_gpio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_iwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_a.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_rtc_b.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_scratchpad.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_tamperio.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_wwdt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_interrupt.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_systick.h)(0x66D136B8)
I (..\BSP\usart.h)(0x68873FBA)
I (..\BSP\ccd.h)(0x68873FB8)
I (..\..\10_I2C\board.h)(0x67500006)
I (..\BSP\bsp_mpu6050.h)(0x675001DA)
I (..\BSP\eMPL\inv_mpu.h)(0x66AA1CDE)
I (..\BSP\drv_oled.h)(0x666EA682)
I (..\BSP\huiduo.h)(0x6889AE32)
I (..\BSP\Motor.h)(0x681A111D)
I (..\BSP\Control.h)(0x6889AC1C)
I (..\BSP\ADC.h)(0x688992C6)
F (..\BSP\Motor.h)(0x681A111D)()
F (..\ti\dl_adc12.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_adc12.o -MMD)
I (..\..\source\ti\driverlib\dl_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
F (..\ti\dl_aes.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aes.o -MMD)
I (..\..\source\ti\driverlib\dl_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_aesadv.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aesadv.o -MMD)
I (..\..\source\ti\driverlib\dl_aesadv.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_common.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_common.o -MMD)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
F (..\ti\dl_crc.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crc.o -MMD)
I (..\..\source\ti\driverlib\dl_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_crcp.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crcp.o -MMD)
I (..\..\source\ti\driverlib\dl_crcp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_dac12.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dac12.o -MMD)
I (..\..\source\ti\driverlib\dl_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_dma.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dma.o -MMD)
I (..\..\source\ti\driverlib\dl_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_flashctl.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_flashctl.o -MMD)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_core.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\dl_sysctl.h)(0x66D136B8)
I (..\..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
F (..\ti\dl_i2c.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_i2c.o -MMD)
I (..\..\source\ti\driverlib\dl_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_keystorectl.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_keystorectl.o -MMD)
I (..\..\source\ti\driverlib\dl_keystorectl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_lcd.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lcd.o -MMD)
I (..\..\source\ti\driverlib\dl_lcd.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_lfss.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lfss.o -MMD)
I (..\..\source\ti\driverlib\dl_lfss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_mathacl.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mathacl.o -MMD)
I (..\..\source\ti\driverlib\dl_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_mcan.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mcan.o -MMD)
I (..\..\source\ti\driverlib\dl_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_opa.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_opa.o -MMD)
I (..\..\source\ti\driverlib\dl_opa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_rtc_common.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_rtc_common.o -MMD)
I (..\..\source\ti\driverlib\dl_rtc_common.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_spi.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_spi.o -MMD)
I (..\..\source\ti\driverlib\dl_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_timer.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_timer.o -MMD)
I (..\..\source\ti\driverlib\dl_timera.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_timerg.h)(0x66D136B8)
F (..\ti\dl_trng.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_trng.o -MMD)
I (..\..\source\ti\driverlib\dl_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
F (..\ti\dl_uart.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_uart.o -MMD)
I (..\..\source\ti\driverlib\dl_uart_extend.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_uart_main.h)(0x66D136B8)
F (..\ti\dl_vref.c)(0x66D136B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../../source/third_party/CMSIS/Core/Include -I ../../source -I ../BSP -I ../../10_I2C -I ../BSP/eMPL

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_vref.o -MMD)
I (..\..\source\ti\driverlib\dl_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\msp.h)(0x66D136B8)
I (..\..\source\ti\devices\DeviceFamily.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x66D136B8)
I (..\..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_aes.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_comp.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_crc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_dma.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_oa.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_spi.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_trng.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_uart.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_vref.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x66D136B8)
I (..\..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x66D136B8)
I (..\..\source\ti\driverlib\dl_common.h)(0x66D136B8)
